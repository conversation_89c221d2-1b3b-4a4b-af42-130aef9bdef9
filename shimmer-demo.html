<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Shimmer Demo</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; background: #f8f9fa; padding: 20px; }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .demo-header p {
            color: #666;
            font-size: 16px;
        }
        
        .layout-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .layout-demo {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            height: 200px;
        }
        
        .layout-title {
            background: #f9fafb;
            padding: 10px 15px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        
        /* Shimmer Animation */
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        
        .layout-shimmer-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #ffffff;
            overflow: hidden;
        }
        
        .layout-shimmer {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            background: #ffffff;
            border-radius: 6px;
            overflow: hidden;
            position: relative;
        }
        
        .shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 4px;
        }
        
        /* Header */
        .shimmer-header {
            background: #ffffff;
            border-bottom: 1px solid #e5e7eb;
            padding: 8px 12px;
            flex-shrink: 0;
        }
        
        .shimmer-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .shimmer-logo { width: 60px; height: 16px; }
        .shimmer-nav-items { display: flex; gap: 8px; }
        .shimmer-nav-item { width: 40px; height: 12px; }
        .shimmer-user { width: 20px; height: 20px; border-radius: 50%; }
        
        /* Main */
        .shimmer-main {
            display: flex;
            flex: 1;
            min-height: 80px;
        }
        
        .shimmer-sidebar {
            width: 80px;
            background: #f9fafb;
            border-right: 1px solid #e5e7eb;
            padding: 8px;
            flex-shrink: 0;
        }
        
        .shimmer-sidebar.shimmer-right {
            border-right: none;
            border-left: 1px solid #e5e7eb;
        }
        
        .shimmer-sidebar-title { width: 50px; height: 12px; margin-bottom: 8px; }
        .shimmer-sidebar-item { width: 40px; height: 8px; margin-bottom: 4px; }
        
        .shimmer-body {
            flex: 1;
            padding: 8px;
            background: #ffffff;
        }
        
        .shimmer-title { width: 60%; height: 14px; margin-bottom: 8px; }
        .shimmer-cards { display: grid; grid-template-columns: repeat(3, 1fr); gap: 6px; }
        .shimmer-card-image { width: 100%; height: 30px; margin-bottom: 4px; }
        .shimmer-card-text { width: 80%; height: 8px; margin-bottom: 3px; }
        .shimmer-card-text:last-child { width: 60%; }
        
        /* Footer */
        .shimmer-footer {
            background: #111827;
            padding: 8px;
            flex-shrink: 0;
        }
        
        .shimmer-footer-content {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }
        
        .shimmer-footer-title, .shimmer-footer-link {
            background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }
        
        .shimmer-footer-title { width: 40px; height: 10px; margin-bottom: 4px; }
        .shimmer-footer-link { width: 30px; height: 6px; margin-bottom: 2px; }
        
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        
        .btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 10px;
        }
        
        .btn:hover {
            background: #5856eb;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            color: #0c4a6e;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>Layout Shimmer Animation Demo</h1>
            <p>This demonstrates the shimmer loading effect for different layout types in the artifacts tab</p>
        </div>
        
        <div class="layout-grid">
            <!-- HB Layout -->
            <div class="layout-demo">
                <div class="layout-title">HB Layout (Header + Body)</div>
                <div class="layout-shimmer-container">
                    <div class="layout-shimmer">
                        <div class="shimmer-header">
                            <div class="shimmer-nav">
                                <div class="shimmer-logo shimmer"></div>
                                <div class="shimmer-nav-items">
                                    <div class="shimmer-nav-item shimmer"></div>
                                    <div class="shimmer-nav-item shimmer"></div>
                                </div>
                                <div class="shimmer-user shimmer"></div>
                            </div>
                        </div>
                        <div class="shimmer-main">
                            <div class="shimmer-body">
                                <div class="shimmer-title shimmer"></div>
                                <div class="shimmer-cards">
                                    <div class="shimmer-card">
                                        <div class="shimmer-card-image shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                    </div>
                                    <div class="shimmer-card">
                                        <div class="shimmer-card-image shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                    </div>
                                    <div class="shimmer-card">
                                        <div class="shimmer-card-image shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- HLSB Layout -->
            <div class="layout-demo">
                <div class="layout-title">HLSB Layout (Header + Left Sidebar + Body)</div>
                <div class="layout-shimmer-container">
                    <div class="layout-shimmer">
                        <div class="shimmer-header">
                            <div class="shimmer-nav">
                                <div class="shimmer-logo shimmer"></div>
                                <div class="shimmer-nav-items">
                                    <div class="shimmer-nav-item shimmer"></div>
                                    <div class="shimmer-nav-item shimmer"></div>
                                </div>
                                <div class="shimmer-user shimmer"></div>
                            </div>
                        </div>
                        <div class="shimmer-main">
                            <div class="shimmer-sidebar">
                                <div class="shimmer-sidebar-title shimmer"></div>
                                <div class="shimmer-sidebar-item shimmer"></div>
                                <div class="shimmer-sidebar-item shimmer"></div>
                                <div class="shimmer-sidebar-item shimmer"></div>
                            </div>
                            <div class="shimmer-body">
                                <div class="shimmer-title shimmer"></div>
                                <div class="shimmer-cards">
                                    <div class="shimmer-card">
                                        <div class="shimmer-card-image shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                    </div>
                                    <div class="shimmer-card">
                                        <div class="shimmer-card-image shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                    </div>
                                    <div class="shimmer-card">
                                        <div class="shimmer-card-image shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- HBRSF Layout -->
            <div class="layout-demo">
                <div class="layout-title">HBRSF Layout (Header + Body + Right Sidebar + Footer)</div>
                <div class="layout-shimmer-container">
                    <div class="layout-shimmer">
                        <div class="shimmer-header">
                            <div class="shimmer-nav">
                                <div class="shimmer-logo shimmer"></div>
                                <div class="shimmer-nav-items">
                                    <div class="shimmer-nav-item shimmer"></div>
                                    <div class="shimmer-nav-item shimmer"></div>
                                </div>
                                <div class="shimmer-user shimmer"></div>
                            </div>
                        </div>
                        <div class="shimmer-main">
                            <div class="shimmer-body">
                                <div class="shimmer-title shimmer"></div>
                                <div class="shimmer-cards">
                                    <div class="shimmer-card">
                                        <div class="shimmer-card-image shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                    </div>
                                    <div class="shimmer-card">
                                        <div class="shimmer-card-image shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                    </div>
                                    <div class="shimmer-card">
                                        <div class="shimmer-card-image shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                        <div class="shimmer-card-text shimmer"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="shimmer-sidebar shimmer-right">
                                <div class="shimmer-sidebar-title shimmer"></div>
                                <div class="shimmer-sidebar-item shimmer"></div>
                                <div class="shimmer-sidebar-item shimmer"></div>
                                <div class="shimmer-sidebar-item shimmer"></div>
                            </div>
                        </div>
                        <div class="shimmer-footer">
                            <div class="shimmer-footer-content">
                                <div>
                                    <div class="shimmer-footer-title"></div>
                                    <div class="shimmer-footer-link"></div>
                                    <div class="shimmer-footer-link"></div>
                                </div>
                                <div>
                                    <div class="shimmer-footer-title"></div>
                                    <div class="shimmer-footer-link"></div>
                                    <div class="shimmer-footer-link"></div>
                                </div>
                                <div>
                                    <div class="shimmer-footer-title"></div>
                                    <div class="shimmer-footer-link"></div>
                                    <div class="shimmer-footer-link"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="toggleAnimation()">Toggle Animation</button>
            <button class="btn" onclick="location.reload()">Restart Demo</button>
        </div>
        
        <div class="status">
            ✨ <strong>Shimmer Effect Active:</strong> The animation runs infinitely and adapts to different layout types (HB, HLSB, HBRS, HLSBRSF, etc.) based on the layout key.
        </div>
    </div>
    
    <script>
        function toggleAnimation() {
            const shimmers = document.querySelectorAll('.shimmer');
            shimmers.forEach(shimmer => {
                if (shimmer.style.animationPlayState === 'paused') {
                    shimmer.style.animationPlayState = 'running';
                } else {
                    shimmer.style.animationPlayState = 'paused';
                }
            });
        }
    </script>
</body>
</html>
